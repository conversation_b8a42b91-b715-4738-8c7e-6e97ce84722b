#!/usr/bin/env python3
"""
🚀 FruitShield-YOLO RTX 4090 宇宙至尊超级巨无霸强悍训练脚本 🚀
专门针对RTX 4090 24GB显存优化的终极训练版本

特性：
- 🎯 RTX 4090 24GB显存完美适配
- 🔥 极致性能优化，不浪费一丝算力
- 🧠 智能动态参数调整
- 🎨 超强数据增强策略
- 📊 实时性能监控
- 🛡️ 防过载保护机制
- 🚀 mAP50-95火箭式增长
"""

import os
import sys
import argparse
import warnings
import datetime
import time
import psutil
import gc
from pathlib import Path
import torch
import torch.cuda

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入必要的模块
from ultralytics import YOLO
from register.register_cbam_simple import register_local_cbam

# 过滤警告
warnings.filterwarnings('ignore')

class RTX4090Optimizer:
    """RTX 4090专用优化器"""
    
    def __init__(self):
        self.gpu_memory = 24  # RTX 4090 24GB
        self.max_batch_size = 32  # 理论最大batch size
        self.optimal_batch_size = 16  # 最优batch size
        self.safe_batch_size = 12  # 安全batch size
        
    def get_gpu_info(self):
        """获取GPU信息"""
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            gpu_memory_free = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / (1024**3)
            return gpu_name, gpu_memory, gpu_memory_free
        return None, 0, 0
    
    def optimize_batch_size(self, base_batch_size, imgsz):
        """根据图像尺寸和显存动态优化batch size"""
        if imgsz <= 512:
            return min(self.max_batch_size, base_batch_size * 2)
        elif imgsz <= 640:
            return min(self.optimal_batch_size, base_batch_size)
        elif imgsz <= 800:
            return min(self.safe_batch_size, base_batch_size)
        else:
            return min(8, base_batch_size)
    
    def get_optimal_workers(self):
        """获取最优worker数量"""
        cpu_count = psutil.cpu_count(logical=False)
        return min(16, cpu_count * 2)  # RTX 4090配套高端CPU

def setup_rtx4090_environment():
    """设置RTX 4090专用环境"""
    print("🔧 设置RTX 4090专用训练环境...")
    
    # 设置CUDA优化
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    torch.backends.cuda.matmul.allow_tf32 = True
    torch.backends.cudnn.allow_tf32 = True
    
    # 设置内存管理
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
    
    print("✅ RTX 4090环境优化完成")

def setup_attention_mechanism(attention_type):
    """设置注意力机制"""
    if attention_type == 'cbam':
        print("🔧 注册CBAM注意力模块...")
        register_local_cbam()
        print("✅ CBAM模块注册成功")
        return True
    elif attention_type == 'c2f':
        print("✅ 使用C2f注意力机制")
        return True
    else:
        print("✅ 使用标准YOLOv8架构")
        return True

def get_model_config_path(attention_type, size):
    """获取模型配置路径"""
    if attention_type == 'cbam':
        config_path = f'models/v8/yolov8{size}-cbam-v2.yaml'
        if os.path.exists(config_path):
            return config_path
        else:
            return f'models/v8/yolov8{size}-cbam.yaml'
    elif attention_type == 'c2f':
        return f'models/v8/yolov8{size}-c2f.yaml'
    else:
        return f'yolov8{size}.yaml'

def get_rtx4090_ultimate_params(args, optimizer):
    """获取RTX 4090终极训练参数"""
    
    # 动态优化batch size
    optimal_batch = optimizer.optimize_batch_size(args.batch, args.imgsz)
    optimal_workers = optimizer.get_optimal_workers()
    
    # 生成实验名称
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name = f"rtx4090_ultimate_{args.attention}_yolov8{args.size}_{timestamp}"
    
    return {
        # 🚀 RTX 4090专用基础参数
        'epochs': args.epochs,
        'batch': optimal_batch,
        'imgsz': args.imgsz,
        'device': 0,
        'workers': optimal_workers,
        'project': 'saved_models',
        'name': experiment_name,
        
        # 🔥 超强学习率策略
        'lr0': 0.003,           # 更高初始学习率，充分利用4090算力
        'lrf': 0.08,            # 更高最终学习率因子
        'momentum': 0.95,       # 更高动量
        'weight_decay': 0.0002, # 更低权重衰减
        
        # 🎯 智能预热策略
        'warmup_epochs': 8.0,   # 更长预热，充分激活网络
        'warmup_momentum': 0.9,
        'warmup_bias_lr': 0.15,
        
        # 🎨 超强数据增强（RTX 4090可以承受更强增强）
        'hsv_h': 0.03,          # 更强色调增强
        'hsv_s': 0.9,           # 更强饱和度增强
        'hsv_v': 0.6,           # 更强明度增强
        'degrees': 30.0,        # 更大旋转角度
        'translate': 0.2,       # 更大平移范围
        'scale': 0.8,           # 更大缩放范围
        'shear': 10.0,          # 添加剪切变换
        'perspective': 0.0005,  # 添加透视变换
        'fliplr': 0.5,
        'flipud': 0.1,          # 添加垂直翻转
        'mosaic': 1.0,
        'mixup': 0.2,           # 更强mixup
        'copy_paste': 0.3,      # 更强copy-paste
        
        # 💪 优化的损失权重
        'box': 8.0,             # 更高box损失权重
        'cls': 0.6,             # 更高分类损失权重
        'dfl': 2.0,             # 更高DFL损失权重
        
        # 🛡️ 智能早停和保存策略
        'patience': 60,         # 更高耐心值
        'save': True,
        'save_period': 5,       # 更频繁保存
        'val': True,
        'plots': True,
        'verbose': True,
        
        # ⚡ RTX 4090专用优化器设置
        'optimizer': 'AdamW',
        'close_mosaic': 20,     # 更晚关闭mosaic
        
        # 🎯 多尺度和精度优化
        'rect': False,
        'single_cls': False,
        'amp': True,            # 启用混合精度
        'fraction': 1.0,        # 使用全部数据
        
        # 🚀 RTX 4090专用高级设置
        'overlap_mask': True,
        'mask_ratio': 4,
        'dropout': 0.0,         # 不使用dropout，让4090全力输出
        'label_smoothing': 0.1, # 标签平滑
    }

def create_enhanced_class_weights():
    """创建增强的类别权重"""
    class_weights = {
        0: 1.3,  # Alternaria_Boltch - 增强权重
        1: 2.0,  # Grey_spot - 最高权重
        2: 1.8,  # Mosaic - 高权重
        3: 1.7,  # Rust - 高权重
        4: 0.7,  # Brown_Spot - 降低权重
    }
    
    weights_file = 'rtx4090_class_weights.txt'
    with open(weights_file, 'w') as f:
        for class_id, weight in class_weights.items():
            f.write(f"{class_id}: {weight}\n")
    
    print(f"✅ RTX 4090专用类别权重文件已创建: {weights_file}")
    return weights_file

def monitor_training_performance():
    """监控训练性能"""
    if torch.cuda.is_available():
        gpu_memory_used = torch.cuda.memory_allocated(0) / (1024**3)
        gpu_memory_cached = torch.cuda.memory_reserved(0) / (1024**3)
        gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)

        # 计算内存使用率
        memory_usage_percent = (gpu_memory_used / gpu_memory_total) * 100

        print(f"🖥️ GPU内存使用: {gpu_memory_used:.2f}GB / {gpu_memory_total:.1f}GB ({memory_usage_percent:.1f}%)")
        print(f"🔥 GPU缓存: {gpu_memory_cached:.2f}GB")

        # 内存使用建议
        if memory_usage_percent > 90:
            print("⚠️ 警告：GPU内存使用率过高，建议减少batch_size")
        elif memory_usage_percent < 60:
            print("💡 提示：GPU内存使用率较低，可以考虑增加batch_size")
        else:
            print("✅ GPU内存使用率良好")

def cleanup_gpu_memory():
    """清理GPU内存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()
        print("🧹 GPU内存清理完成")

def train_model(args):
    """RTX 4090终极训练函数"""
    print("🚀 启动RTX 4090宇宙至尊超级巨无霸强悍训练！")
    print("=" * 80)
    
    # 初始化RTX 4090优化器
    optimizer = RTX4090Optimizer()
    
    # 获取GPU信息
    gpu_name, gpu_memory, gpu_memory_free = optimizer.get_gpu_info()
    print(f"🖥️ GPU: {gpu_name}")
    print(f"💾 显存: {gpu_memory:.1f}GB (可用: {gpu_memory_free:.1f}GB)")
    
    if "4090" not in gpu_name:
        print("⚠️ 警告：未检测到RTX 4090，但将使用4090优化参数")
    
    # 设置RTX 4090环境
    setup_rtx4090_environment()
    
    # 设置注意力机制
    if not setup_attention_mechanism(args.attention):
        print("⚠️ 注意力机制设置失败，使用标准YOLOv8")
    
    # 获取模型配置
    model_config_path = get_model_config_path(args.attention, args.size)
    print(f"📁 模型配置: {model_config_path}")
    
    # 验证配置文件
    if not os.path.exists(model_config_path):
        print(f"❌ 配置文件不存在: {model_config_path}")
        return False
    
    # 创建增强类别权重
    create_enhanced_class_weights()
    
    # 获取RTX 4090终极训练参数
    training_params = get_rtx4090_ultimate_params(args, optimizer)
    
    print(f"\n🎯 RTX 4090终极训练配置:")
    print(f"📦 批次大小: {training_params['batch']} (动态优化)")
    print(f"👥 工作进程: {training_params['workers']}")
    print(f"📈 学习率: {training_params['lr0']} -> {training_params['lr0'] * training_params['lrf']}")
    print(f"🔥 预热轮次: {training_params['warmup_epochs']}")
    print(f"🎨 数据增强: 超强模式")
    print(f"💾 实验名称: {training_params['name']}")
    
    # 初始化模型
    try:
        if os.path.exists(model_config_path):
            model = YOLO(model_config_path)
            if args.weights and args.weights != 'None':
                print(f"📥 加载预训练权重: {args.weights}")
                model = model.load(args.weights)
        else:
            model = YOLO(args.weights if args.weights else 'weights/yolov8s.pt')
        
        print("✅ 模型初始化成功")
        
    except Exception as e:
        print(f"❌ 模型初始化失败: {e}")
        return False
    
    # 设置数据集路径
    data_path = args.data if args.data else 'datasets/integrated_dataset/dataset.yaml'
    if not os.path.exists(data_path):
        print(f"❌ 数据集配置文件不存在: {data_path}")
        return False
    
    training_params['data'] = data_path
    
    # 开始RTX 4090终极训练
    try:
        print("\n🚀 RTX 4090终极训练开始！")
        print("=" * 80)
        print("🎯 预期性能目标:")
        print("   📊 30轮内 mAP50-95 > 0.45")
        print("   📊 50轮内 mAP50-95 > 0.50")
        print("   📊 100轮内 mAP50-95 > 0.55")
        print("=" * 80)
        
        # 监控训练前性能
        monitor_training_performance()
        
        # 开始训练
        start_time = time.time()
        results = model.train(**training_params)
        end_time = time.time()
        
        # 训练完成
        training_time = end_time - start_time
        print(f"\n🎉 RTX 4090终极训练完成！")
        print(f"⏱️ 训练时间: {training_time/3600:.2f} 小时")
        print(f"📊 最佳模型: {model.trainer.best}")
        print(f"🚀 平均每轮时间: {training_time/args.epochs:.2f} 秒")
        
        # 最终性能监控
        monitor_training_performance()
        
        return True
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='🚀 RTX 4090宇宙至尊超级巨无霸强悍训练脚本')
    
    # 基本配置
    parser.add_argument('--attention', type=str, default='cbam', 
                      choices=['cbam', 'c2f', 'none'],
                      help='注意力机制类型')
    parser.add_argument('--size', type=str, default='s',
                      choices=['n', 's', 'm', 'l', 'x'],
                      help='模型大小')
    parser.add_argument('--epochs', type=int, default=200,
                      help='训练轮次 (RTX 4090推荐200+)')
    parser.add_argument('--batch', type=int, default=16,
                      help='基础批次大小 (将动态优化)')
    parser.add_argument('--imgsz', type=int, default=640,
                      help='图像尺寸')
    parser.add_argument('--weights', type=str, default='weights/yolov8s.pt',
                      help='预训练权重路径')
    parser.add_argument('--data', type=str, default='datasets/integrated_dataset/dataset.yaml',
                      help='数据集配置文件路径')
    
    args = parser.parse_args()
    
    print("🚀" * 20)
    print("RTX 4090 宇宙至尊超级巨无霸强悍训练脚本")
    print("🚀" * 20)
    
    # 执行训练
    success = train_model(args)
    
    if success:
        print("\n🎉 RTX 4090终极训练成功完成！")
        print("🏆 您已解锁宇宙至尊训练成就！")
        print("📊 建议查看训练曲线，mAP50-95应该有显著提升！")
        sys.exit(0)
    else:
        print("\n❌ 训练失败，请检查配置")
        sys.exit(1)

if __name__ == "__main__":
    main()
